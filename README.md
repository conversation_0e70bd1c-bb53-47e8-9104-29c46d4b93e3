# 浙江益肤皮肤病医院 H5 页面重构

## 重构说明

本项目使用 Vant 4.x 组件库对原有的医院 H5 页面进行了重构，使界面更加简约大气，同时保持了原有的功能和视觉效果。

## 主要改进

### 1. 技术栈升级
- **Vue 3**: 使用最新的 Vue 3 框架
- **Vant 4.x**: 采用有赞前端团队开发的移动端组件库
- **现代化CSS**: 使用CSS自定义属性和现代布局

### 2. 界面优化
- **简约设计**: 使用 Vant 组件统一界面风格
- **渐变背景**: Banner 区域采用优雅的渐变色背景
- **圆角设计**: 统一使用圆角设计语言
- **间距优化**: 调整各模块间距，提升视觉层次

### 3. 组件化重构
- **优惠券区域**: 使用 `van-cell` 组件重构优惠券展示
- **医生介绍**: 使用 `van-cell` 和 `van-image` 组件
- **预约表单**: 使用 `van-form` 和 `van-field` 组件
- **底部导航**: 使用 `van-tabbar` 组件
- **页面导航**: 使用 `van-nav-bar` 组件

### 4. 交互优化
- **表单验证**: 集成 Vant 的表单验证功能
- **选择器**: 使用 `van-picker` 实现病情选择
- **提示信息**: 使用 `van-toast` 统一提示样式
- **页面切换**: 添加平滑的页面切换动画

## 文件结构

```
├── index.html          # 主页面文件
├── style.css           # 样式文件
├── script.js           # JavaScript 逻辑
├── logo.svg            # 医院 Logo
├── doctor1.svg         # 医生头像
└── README.md           # 说明文档
```

## 主要功能

1. **首页展示**: 医院信息和优惠券展示
2. **优惠券领取**: 三种不同类型的优惠券
3. **医生咨询**: 医生信息展示和咨询功能
4. **预约挂号**: 完整的预约表单
5. **个人中心**: 用户信息和功能入口
6. **我的预约**: 预约记录查看
7. **我的优惠券**: 优惠券管理

## 设计特色

- **色彩搭配**: 主色调采用渐变蓝紫色 (#667eea → #764ba2)
- **视觉层次**: 通过卡片、阴影、间距营造层次感
- **响应式设计**: 适配不同尺寸的移动设备
- **用户体验**: 流畅的交互动画和反馈

## 运行方式

1. 启动本地服务器：
   ```bash
   python3 -m http.server 8000
   ```

2. 在浏览器中访问：
   ```
   http://localhost:8000
   ```

## 浏览器兼容性

- iOS Safari 10+
- Android Chrome 50+
- 微信内置浏览器
- 支付宝内置浏览器

## 技术特点

- **轻量级**: 使用 CDN 引入依赖，加载速度快
- **组件化**: 高度组件化的代码结构
- **可维护**: 清晰的代码组织和注释
- **可扩展**: 基于 Vue 3 和 Vant 的可扩展架构
