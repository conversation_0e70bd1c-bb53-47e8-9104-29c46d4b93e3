const { createApp } = Vue;

createApp({
    data() {
        return {
            activeTab: 'home',
            form: {
                name: '',
                phone: '',
                condition: '',
                description: ''
            },

        }
    },
    mounted() {
        this.showPage('home');
    },
    methods: {
        // 页面切换
        showPage(page, event) {
            document.querySelectorAll('.page').forEach(p => p.classList.add('hidden'));
            document.getElementById(page + 'Page').classList.remove('hidden');

            // 更新导航状态
            document.querySelectorAll('.nav-item').forEach(item => item.classList.remove('active'));
            if (event && event.target) {
                const navItem = event.target.closest('.nav-item');
                if (navItem) {
                    navItem.classList.add('active');
                }
            }
        },

        // 显示我的优惠券
        showMyCoupons() {
            this.showPage('coupons');
        },

        // 显示我的预约
        showMyAppointments() {
            this.showPage('appointments');
        },

        // 返回上一页
        goBack() {
            this.showPage('profile');
        },

        // 优惠券标签切换
        switchTab(type, event) {
            // 更新标签状态
            document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
            if (event && event.target) {
                event.target.classList.add('active');
            }

            // 切换内容
            if (type === 'unused') {
                document.getElementById('unusedCoupons').classList.remove('hidden');
                document.getElementById('usedCoupons').classList.add('hidden');
            } else {
                document.getElementById('unusedCoupons').classList.add('hidden');
                document.getElementById('usedCoupons').classList.remove('hidden');
            }
        },

        // 优惠券领取
        receiveCoupon(couponId) {
            console.log('领取优惠券:', couponId);
            vant.showToast('优惠券领取成功！');
        },

        // 使用优惠券
        useCoupon(couponIndex) {
            console.log('使用优惠券:', couponIndex);
            vant.showToast('跳转到使用页面');
        },

        // 咨询医生
        consultDoctor() {
            vant.showToast('正在为您接通医生...');
        },



        // 表单提交
        onSubmit() {
            if (!this.form.name || !this.form.phone || !this.form.condition) {
                vant.showToast('请填写完整信息');
                return;
            }

            vant.showToast('预约提交成功，我们会尽快联系您！');
            this.form = {
                name: '',
                phone: '',
                condition: '',
                description: ''
            };
        }
    }
}).use(vant).mount('body');